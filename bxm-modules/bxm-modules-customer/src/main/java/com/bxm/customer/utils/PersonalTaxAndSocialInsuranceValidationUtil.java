package com.bxm.customer.utils;

import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.vo.valueAdded.SocialInsuranceVO;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.helper.ValueAddedEmpValidationHelper;
import com.bxm.customer.service.IValueAddedEmployeeService;

import java.math.BigDecimal;
import java.util.regex.Pattern;

/**
 * 个税明细和社医保业务共用的校验工具类
 * 
 * 根据业务需求提供统一的校验规则：
 * 1. 姓名：必填，2-50个字，至少填2个字
 * 2. 身份证号：必填，18个数字，同一交付单内身份证号唯一
 * 3. 手机号：必填，11个数字，同一交付单内唯一
 * 4. 申报基数：显示前置条件：方式为增员或更正时。必填，浮点2位数字
 * 5. 申报险种：显示前置条件：方式为增员或更正时。必填，至少有一个选中，默认全选中
 * 6. 备注：当选择申报险种为：其他。备注必填
 *
 * <AUTHOR>
 * @date 2025-08-23
 */
public class PersonalTaxAndSocialInsuranceValidationUtil {

    // 手机号正则：11位数字
    private static final Pattern MOBILE_PATTERN = Pattern.compile("^\\d{11}$");
    
    // 身份证号正则：18位数字
    private static final Pattern ID_NUMBER_PATTERN = Pattern.compile("^\\d{18}$");

    /**
     * 校验员工姓名
     * 规则：必填，2-50个字，至少填2个字
     *
     * @param employeeName 员工姓名
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateEmployeeName(String employeeName) {
        if (StringUtils.isEmpty(employeeName)) {
            throw new IllegalArgumentException("员工姓名不能为空");
        }
        
        String trimmedName = employeeName.trim();
        if (trimmedName.length() < 2) {
            throw new IllegalArgumentException("员工姓名至少需要2个字符");
        }
        
        if (trimmedName.length() > 50) {
            throw new IllegalArgumentException("员工姓名不能超过50个字符");
        }
    }

    /**
     * 校验身份证号格式
     * 规则：必填，18个数字
     *
     * @param idNumber 身份证号
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateIdNumber(String idNumber) {
        if (StringUtils.isEmpty(idNumber)) {
            throw new IllegalArgumentException("身份证号不能为空");
        }
        
        String trimmedIdNumber = idNumber.trim();
        if (!ID_NUMBER_PATTERN.matcher(trimmedIdNumber).matches()) {
            throw new IllegalArgumentException("身份证号必须为18位数字");
        }
    }

    /**
     * 校验手机号格式
     * 规则：必填，11个数字
     *
     * @param mobile 手机号
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            throw new IllegalArgumentException("手机号不能为空");
        }
        
        String trimmedMobile = mobile.trim();
        if (!MOBILE_PATTERN.matcher(trimmedMobile).matches()) {
            throw new IllegalArgumentException("手机号必须为11位数字");
        }
    }

    /**
     * 校验申报基数
     * 规则：方式为增员或更正时必填，浮点2位数字
     *
     * @param socialInsuranceBase 申报基数
     * @param operationType 操作类型
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateSocialInsuranceBase(BigDecimal socialInsuranceBase, Integer operationType) {
        // 检查是否为增员或更正操作
        if (isAddOrCorrectionOperation(operationType)) {
            if (socialInsuranceBase == null) {
                throw new IllegalArgumentException("方式为增员或更正时，申报基数不能为空");
            }
            
            if (socialInsuranceBase.compareTo(BigDecimal.ZERO) < 0) {
                throw new IllegalArgumentException("申报基数不能为负数");
            }
            
            // 检查小数位数不超过2位
            if (socialInsuranceBase.scale() > 2) {
                throw new IllegalArgumentException("申报基数最多保留2位小数");
            }
        }
    }

    /**
     * 校验备注
     * 规则：当选择申报险种为"其他"时，备注必填
     *
     * @param remark 备注
     * @param socialInsurance 社保信息
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateRemark(String remark, SocialInsuranceVO socialInsurance) {
        // 检查是否选择了"其他"险种
        if (socialInsurance != null && socialInsurance.getQiTa() != null && socialInsurance.getQiTa()) {
            if (StringUtils.isEmpty(remark)) {
                throw new IllegalArgumentException("当选择申报险种为其他时，备注不能为空");
            }
        }
    }

    /**
     * 校验同一交付单内的唯一性
     * 规则：同一交付单内身份证号唯一，手机号唯一
     *
     * @param employeeVO 员工信息VO
     * @param service 员工服务
     * @param helper 校验助手
     * @param bizType 业务类型
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateUniqueness(ValueAddedEmployeeVO employeeVO, 
                                        IValueAddedEmployeeService service,
                                        ValueAddedEmpValidationHelper helper, 
                                        Integer bizType) {
        // 校验身份证号唯一性
        if (!helper.checkIdNumberUniquenessInDeliveryOrder(
                employeeVO.getDeliveryOrderNo(), 
                employeeVO.getIdNumber(), 
                bizType, 
                employeeVO.getId())) {
            throw new IllegalArgumentException("同一交付单内身份证号不能重复");
        }
        
        // 校验手机号唯一性
        if (!helper.checkMobileUniquenessInDeliveryOrder(
                employeeVO.getDeliveryOrderNo(), 
                employeeVO.getMobile(), 
                bizType, 
                employeeVO.getId())) {
            throw new IllegalArgumentException("同一交付单内手机号不能重复");
        }
    }

    /**
     * 判断操作类型是否为增员或更正
     *
     * @param operationType 操作类型
     * @return 是否为增员或更正操作
     */
    private static boolean isAddOrCorrectionOperation(Integer operationType) {
        return operationType != null && (operationType == 1 || operationType == 2); // 1-增员, 2-更正
    }
}
